"""
Enhanced Gemini Integration with Historical/Momentum Balance Learning
Integrates the enhanced adaptive learning system with Gemini API
Now includes tournament level integration for better predictions
"""

import json
from typing import Dict, Any, Tu<PERSON>, Optional
from datetime import datetime

from gemini_api import GeminiTennisAnalyzer
from enhanced_adaptive_learning_system import EnhancedAdaptiveLearningSystem
from player_data_parser import player_parser

# Import enhanced learning system if available
try:
    from learning_system_integration import enhanced_gui_integration, learning_integrator
    ENHANCED_LEARNING_V2_AVAILABLE = True
except ImportError:
    ENHANCED_LEARNING_V2_AVAILABLE = False


class EnhancedGeminiAnalyzer(GeminiTennisAnalyzer):
    """Enhanced Gemini analyzer with historical/momentum balance learning"""
    
    def __init__(self):
        super().__init__()
        self.enhanced_learning_system = EnhancedAdaptiveLearningSystem()
        self._last_prediction_id = None
        self._last_prediction_context = None
    
    def _extract_historical_factors(self, historical_profiles: str, player1_name: str, player2_name: str) -> Dict[str, Any]:
        """Extract key historical factors from player profiles"""
        factors = {}
        
        # Parse historical data for both players
        try:
            # Extract break point conversion rates
            bp_conversion_pattern = r'Break Point Conversion Rate: ([\d.]+)%'
            bp_save_pattern = r'Break Point Save Rate: ([\d.]+)%'
            service_hold_pattern = r'Service Hold Rate: ([\d.]+)%'
            surface_win_pattern = r'Surface Win Rate: ([\d.]+)%'
            clutch_pattern = r'Clutch Performance vs Ranked: ([\d.]+)%'
            
            import re
            
            # Extract for player 1
            p1_bp_conv = re.search(bp_conversion_pattern, historical_profiles)
            p1_bp_save = re.search(bp_save_pattern, historical_profiles)
            p1_service_hold = re.search(service_hold_pattern, historical_profiles)
            p1_surface_win = re.search(surface_win_pattern, historical_profiles)
            p1_clutch = re.search(clutch_pattern, historical_profiles)
            
            factors[f'{player1_name}_break_point_conversion'] = float(p1_bp_conv.group(1)) if p1_bp_conv else 50.0
            factors[f'{player1_name}_break_point_save'] = float(p1_bp_save.group(1)) if p1_bp_save else 50.0
            factors[f'{player1_name}_service_hold_rate'] = float(p1_service_hold.group(1)) if p1_service_hold else 75.0
            factors[f'{player1_name}_surface_win_rate'] = float(p1_surface_win.group(1)) if p1_surface_win else 50.0
            factors[f'{player1_name}_clutch_performance'] = float(p1_clutch.group(1)) if p1_clutch else 50.0
            
            # Calculate relative advantages
            factors['break_point_conversion_advantage'] = (
                factors[f'{player1_name}_break_point_conversion'] - 
                factors.get(f'{player2_name}_break_point_conversion', 50.0)
            )
            factors['service_hold_advantage'] = (
                factors[f'{player1_name}_service_hold_rate'] - 
                factors.get(f'{player2_name}_service_hold_rate', 75.0)
            )
            factors['clutch_performance_advantage'] = (
                factors[f'{player1_name}_clutch_performance'] - 
                factors.get(f'{player2_name}_clutch_performance', 50.0)
            )
            
        except Exception as e:
            print(f"Error extracting historical factors: {e}")
            # Provide defaults
            factors = {
                'break_point_conversion_advantage': 0.0,
                'service_hold_advantage': 0.0,
                'clutch_performance_advantage': 0.0
            }
        
        return factors
    
    def _extract_momentum_factors(self, serve_patterns: Dict, player1_code: str, player2_code: str) -> Dict[str, Any]:
        """Extract key momentum factors from serve patterns"""
        factors = {}
        
        try:
            p1_pattern = serve_patterns.get(player1_code, {})
            p2_pattern = serve_patterns.get(player2_code, {})
            
            # Extract momentum metrics
            p1_momentum_intensity = self._get_momentum_intensity(p1_pattern)
            p2_momentum_intensity = self._get_momentum_intensity(p2_pattern)
            
            p1_service_consistency = self._get_service_consistency(p1_pattern)
            p2_service_consistency = self._get_service_consistency(p2_pattern)
            
            p1_mental_fatigue = self._get_mental_fatigue(p1_pattern)
            p2_mental_fatigue = self._get_mental_fatigue(p2_pattern)
            
            # Calculate relative advantages
            factors['momentum_intensity_advantage'] = p1_momentum_intensity - p2_momentum_intensity
            factors['service_consistency_advantage'] = p1_service_consistency - p2_service_consistency
            factors['mental_fatigue_advantage'] = p2_mental_fatigue - p1_mental_fatigue  # Lower fatigue is better
            
            # Recent performance indicators
            p1_recent_runs = p1_pattern.get('recent_three_point_runs', 0)
            p2_recent_runs = p2_pattern.get('recent_three_point_runs', 0)
            factors['recent_momentum_advantage'] = p1_recent_runs - p2_recent_runs
            
            # Pressure handling
            p1_clutch = p1_pattern.get('clutch_performance', 50.0)
            p2_clutch = p2_pattern.get('clutch_performance', 50.0)
            factors['current_clutch_advantage'] = p1_clutch - p2_clutch
            
        except Exception as e:
            print(f"Error extracting momentum factors: {e}")
            factors = {
                'momentum_intensity_advantage': 0.0,
                'service_consistency_advantage': 0.0,
                'mental_fatigue_advantage': 0.0,
                'recent_momentum_advantage': 0.0,
                'current_clutch_advantage': 0.0
            }
        
        return factors
    
    def _get_momentum_intensity(self, pattern: Dict) -> float:
        """Extract momentum intensity from pattern"""
        if hasattr(pattern, 'momentum_intensity'):
            return getattr(pattern.momentum_intensity, 'intensity_score', 5.0)
        return pattern.get('momentum_intensity_score', 5.0)
    
    def _get_service_consistency(self, pattern: Dict) -> float:
        """Extract service consistency from pattern"""
        if hasattr(pattern, 'service_consistency'):
            return pattern.service_consistency
        return pattern.get('service_consistency', 5.0)
    
    def _get_mental_fatigue(self, pattern: Dict) -> float:
        """Extract mental fatigue from pattern"""
        if hasattr(pattern, 'mental_fatigue'):
            return pattern.mental_fatigue
        return pattern.get('mental_fatigue', 25.0)
    
    def create_enhanced_analysis_prompt(self, set_number: int, current_score: Tuple[int, int],
                                      player1_name: str, player1_code: str,
                                      player2_name: str, player2_code: str,
                                      serve_patterns: Dict, match_context: Dict = None) -> Tuple[str, Dict[str, Any]]:
        """Create enhanced analysis prompt with learned historical/momentum balance"""
        
        # Get prediction context
        prediction_context = {
            'surface': match_context.get('surface') if match_context else 'Hard',
            'set_number': set_number,
            'score': current_score,
            'match_context': match_context
        }
        
        # Get optimal balance for this context
        optimal_balance = self.enhanced_learning_system.get_optimal_balance_for_context(prediction_context)
        
        # Format the current score
        score_text = f"{current_score[0]}-{current_score[1]}"
        
        # Get set number as ordinal
        set_ordinals = {1: "First", 2: "Second", 3: "Third", 4: "Fourth", 5: "Fifth"}
        set_text = set_ordinals.get(set_number, f"Set {set_number}")
        
        # Determine next server
        next_server = self._determine_next_server(serve_patterns, player1_code, player2_code, player1_name, player2_name)
        
        # Format live momentum statistics for both players
        player1_patterns = self.format_serving_patterns(serve_patterns, player1_code, player1_name)
        player2_patterns = self.format_serving_patterns(serve_patterns, player2_code, player2_name)
        
        # Get surface from match context
        surface = match_context.get('surface') if match_context else "Unknown"
        
        # Get historical player profiles with surface-specific data
        historical_profiles = player_parser.get_both_player_profiles_for_prompt(player1_name, player2_name, surface)
        
        # Extract factors for learning
        historical_factors = self._extract_historical_factors(historical_profiles, player1_name, player2_name)
        momentum_factors = self._extract_momentum_factors(serve_patterns, player1_code, player2_code)
        
        # Get adaptive weights for momentum factors (from base system)
        adaptive_weights = self.weights_manager.get_weights_for_prediction(prediction_context)
        
        # Determine score-specific adjustments
        score_adjustments = self._get_score_specific_adjustments(current_score, adaptive_weights)
        
        # Calculate serve advantage
        serve_advantage = self._calculate_serve_advantage(current_score, next_server, player1_name, player2_name)
        
        # Get tournament level information
        tournament_level = match_context.get('tournament_level', 'Mixed') if match_context else 'Mixed'
        tournament_name = match_context.get('tournament_name', '') if match_context else ''

        # Get enhanced weights if available
        enhanced_weights = None
        if ENHANCED_LEARNING_V2_AVAILABLE:
            try:
                weight_result = enhanced_gui_integration['get_prediction_weights'](
                    surface=surface,
                    tournament_name=tournament_name,
                    additional_context={
                        'tournament_level': tournament_level,
                        'set_number': set_number,
                        'score': current_score
                    }
                )
                enhanced_weights = weight_result.get('weights', {})
            except Exception as e:
                print(f"Could not get enhanced weights: {e}")

        # Create the enhanced prompt with learned balance and tournament context
        tournament_context = f"Tournament Level: {tournament_level}"
        if tournament_name:
            tournament_context += f" ({tournament_name})"

        # Get actual count of learning-eligible predictions
        learning_eligible_count = len(self.enhanced_learning_system.get_learning_eligible_predictions())

        prompt = f"""IMPORTANT: Your response must contain ONLY two lines with player names and percentages. NO other text allowed.

You are an advanced tennis prediction calculator using LEARNED ADAPTIVE WEIGHTING that has been optimized based on {learning_eligible_count} historical predictions with TOURNAMENT-SPECIFIC LEARNING.

## **LEARNED OPTIMAL WEIGHTING FOR THIS CONTEXT**
**Context: {score_text} in {set_text} Set on {surface}**
**{tournament_context}**
- Historical Player Data Weight: {optimal_balance['historical']:.0%} (LEARNED OPTIMAL)
- Live Momentum Data Weight: {optimal_balance['momentum']:.0%} (LEARNED OPTIMAL)
- Balance Version: {self.enhanced_learning_system.current_balance.version}
{"- Enhanced Learning: ACTIVE" if enhanced_weights else "- Enhanced Learning: FALLBACK"}

## **CURRENT MATCH SITUATION**

{player1_name} vs. {player2_name} – {score_text}, {set_text} Set
Court Surface: {surface}
{tournament_context}
Next Server: {next_server}
Serve Advantage: {serve_advantage['base_advantage']:.1%} to next server

### {player1_name} Live Momentum Statistics:
{player1_patterns}

### {player2_name} Live Momentum Statistics:
{player2_patterns}

## **HISTORICAL PLAYER PROFILES**

{historical_profiles}

## **PREDICTION OBJECTIVE**

**🎯 TARGET: Predict who will WIN THE CURRENT {set_text.upper()} SET**

## **LEARNED CALCULATION ALGORITHM**

Based on analysis of {self.enhanced_learning_system.current_balance.sample_size} similar predictions, use this LEARNED optimal approach:

### **STEP 1: Calculate Historical Baseline ({optimal_balance['historical']:.0%} Weight)**

For each player, establish baseline using the most predictive historical factors:
- Break Point Conversion Rate: Weight 0.30 (learned as highly predictive)
- Service Hold Rate on {surface}: Weight 0.25
- Clutch Performance vs Ranked: Weight 0.25 (learned as critical in {set_text} set)
- Surface Win Rate: Weight 0.20

Historical_Score = (BP_Conversion × 0.30) + (Service_Hold × 0.25) + (Clutch × 0.25) + (Surface_Rate × 0.20)

### **STEP 2: Calculate Live Momentum Score ({optimal_balance['momentum']:.0%} Weight)**

Apply learned momentum factor weights (optimized for {tournament_level} {set_text} set):
- Service Consistency: Weight {enhanced_weights.get('service_consistency_weight', score_adjustments['service_consistency_weight']):.2f}
- Mental Fatigue Differential: Weight {enhanced_weights.get('mental_fatigue_weight', score_adjustments['mental_fatigue_weight']):.2f}
- Momentum Intensity: Weight {enhanced_weights.get('momentum_intensity_weight', 0.20):.2f}
- Service Pressure: Weight {enhanced_weights.get('service_pressure_weight', 0.15):.2f}

Live_Momentum_Score = (Service_Consistency × {enhanced_weights.get('service_consistency_weight', score_adjustments['service_consistency_weight']):.2f}) +
                     (Mental_Fatigue_Diff × {enhanced_weights.get('mental_fatigue_weight', score_adjustments['mental_fatigue_weight']):.2f}) +
                     (Momentum_Intensity × {enhanced_weights.get('momentum_intensity_weight', 0.20):.2f}) +
                     (Service_Pressure × {enhanced_weights.get('service_pressure_weight', 0.15):.2f})

### **STEP 3: Apply Tournament-Specific Learned Integration Formula**

**LEARNED OPTIMAL INTEGRATION for {tournament_level} {score_text} in {set_text} Set:**
Final_Probability = (Historical_Score × {optimal_balance['historical']:.0%}) + (Live_Momentum_Score × {optimal_balance['momentum']:.0%})

This weighting has been learned from {self.enhanced_learning_system.current_balance.sample_size} similar predictions and optimized specifically for {tournament_level} matches on {surface} courts.

### **STEP 4: Apply Context Multipliers**

- Score Context Multiplier: {score_adjustments['context_multiplier']:.1f}x
- Break Point Pressure: {score_adjustments['break_point_pressure']:.1f}x

## **MANDATORY OUTPUT FORMAT**

Provide ONLY these two lines:
{player1_name}: XX.X%
{player2_name}: YY.Y%"""
        
        # Store context for outcome recording
        self._last_prediction_context = {
            'prediction_context': prediction_context,
            'historical_factors': historical_factors,
            'momentum_factors': momentum_factors,
            'balance_used': optimal_balance,
            'adaptive_weights': adaptive_weights.to_dict()
        }
        
        return prompt, optimal_balance

    def analyze_set_with_enhanced_learning(self, set_number: int, current_score: Tuple[int, int],
                                         player1_name: str, player1_code: str,
                                         player2_name: str, player2_code: str,
                                         serve_patterns: Dict, match_context: Dict = None) -> Dict[str, Any]:
        """Analyze set with enhanced historical/momentum learning"""

        try:
            # Create enhanced prompt with learned balance
            prompt, balance_used = self.create_enhanced_analysis_prompt(
                set_number, current_score, player1_name, player1_code,
                player2_name, player2_code, serve_patterns, match_context
            )

            # Log the enhanced prompt
            print("\n" + "="*80)
            print("🧠 ENHANCED GEMINI AI ANALYSIS REQUEST (With Learned Balance)")
            print("="*80)
            print(f"Historical Weight: {balance_used['historical']:.0%}")
            print(f"Momentum Weight: {balance_used['momentum']:.0%}")
            print(f"Balance Version: {self.enhanced_learning_system.current_balance.version}")
            print("-"*40)
            print("ENHANCED PROMPT:")
            print(prompt[:500] + "..." if len(prompt) > 500 else prompt)
            print("-"*40)
            print("="*80)

            # Get response from Gemini
            import google.generativeai as genai
            generation_config = genai.types.GenerationConfig(
                temperature=0.0,
                max_output_tokens=65536,
            )

            response = self.model.generate_content(prompt, generation_config=generation_config)
            analysis_text = response.text

            # Parse probabilities
            player1_prob, player2_prob = self.parse_probabilities(
                analysis_text, player1_name, player2_name, player1_code, player2_code
            )

            # Record prediction for learning
            predicted_winner = player1_name if player1_prob > player2_prob else player2_name
            confidence = max(player1_prob, player2_prob)

            prediction_id = self.enhanced_learning_system.record_contextual_prediction(
                prediction_context=self._last_prediction_context['prediction_context'],
                historical_factors=self._last_prediction_context['historical_factors'],
                momentum_factors=self._last_prediction_context['momentum_factors'],
                predicted_winner=predicted_winner,
                balance_used=balance_used,
                confidence=confidence
            )

            self._last_prediction_id = prediction_id

            print(f"\n📊 ENHANCED ANALYSIS RESULTS:")
            print(f"   {player1_name}: {player1_prob:.1%}")
            print(f"   {player2_name}: {player2_prob:.1%}")
            print(f"   Historical/Momentum Balance: {balance_used['historical']:.0%}/{balance_used['momentum']:.0%}")
            print(f"   Prediction ID: {prediction_id}")
            print("="*80)

            return {
                "success": True,
                "analysis": analysis_text,
                "player1_probability": player1_prob,
                "player2_probability": player2_prob,
                "predicted_winner": predicted_winner,
                "model_used": "gemini-2.5-flash-enhanced",
                "balance_used": balance_used,
                "prediction_id": prediction_id,
                "enhanced_learning_version": self.enhanced_learning_system.current_balance.version,
                "adaptive_weights": self._last_prediction_context['adaptive_weights'],
                "prediction_context": self._last_prediction_context['prediction_context']
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"Enhanced API Error: {str(e)}",
                "analysis": "",
                "player1_probability": 0.5,
                "player2_probability": 0.5
            }

    def record_enhanced_prediction_outcome(self, actual_winner: str, prediction_id: str = None):
        """Record the outcome of an enhanced prediction for learning"""
        if prediction_id is None:
            prediction_id = self._last_prediction_id

        if prediction_id:
            self.enhanced_learning_system.record_prediction_outcome(prediction_id, actual_winner)
            print(f"✅ Recorded outcome for prediction {prediction_id}: {actual_winner} won")

            # Check if we should optimize balances
            completed_predictions = [p for p in self.enhanced_learning_system.contextual_predictions
                                   if p.actual_winner is not None]

            if len(completed_predictions) % 20 == 0 and len(completed_predictions) >= 20:
                print(f"🔄 Triggering balance optimization after {len(completed_predictions)} predictions...")
                optimization_result = self.enhanced_learning_system.optimize_balances()

                if optimization_result.get('status') == 'balances_updated':
                    print(f"✅ Balances optimized! New version: {optimization_result['new_version']}")
                    for improvement in optimization_result['improvements']:
                        if 'accuracy_improvement' in improvement:
                            print(f"   {improvement['context']}: {improvement['accuracy_improvement']:.1%} improvement")
                        else:
                            # Fallback for improvements without accuracy_improvement
                            print(f"   {improvement['context']}: configuration updated")
                else:
                    print(f"ℹ️ No significant balance improvements found")
        else:
            print("⚠️ No prediction ID available for outcome recording")

    def get_enhanced_learning_status(self) -> Dict[str, Any]:
        """Get comprehensive enhanced learning status"""
        return self.enhanced_learning_system.get_learning_status()

    def force_balance_optimization(self) -> Dict[str, Any]:
        """Manually trigger balance optimization"""
        return self.enhanced_learning_system.optimize_balances()


# Global enhanced analyzer instance
enhanced_gemini_analyzer = EnhancedGeminiAnalyzer()
